<!-- Multimodal RAG Configuration Partial -->
<div class="space-y-8">
    <!-- Main Multimodal Settings -->
    <div class="config-section">
        <h2 class="config-section-header">Multimodal RAG Settings</h2>
        <p class="config-section-description">
            Configure multimodal content extraction and processing capabilities for documents containing images, tables, and text.
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Enable Multimodal -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="enable_multimodal" name="enable_multimodal"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.enable_multimodal %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Enable Multimodal Processing</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Enable extraction and processing of images and tables from documents
                </p>
            </div>

            <!-- Parallel Processing -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="enable_parallel_processing" name="enable_parallel_processing"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.enable_parallel_processing %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Enable Parallel Processing</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Process images and tables in parallel for better performance
                </p>
            </div>

            <!-- Max Workers -->
            <div>
                <label for="max_workers" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Max Workers
                    <span class="text-red-500">*</span>
                </label>
                <input type="number" id="max_workers" name="max_workers" min="1" max="16" value="{{ multimodal_parameters.max_workers }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Number of parallel workers for multimodal processing
                </p>
            </div>

            <!-- Processing Timeout -->
            <div>
                <label for="processing_timeout" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Processing Timeout (seconds)
                </label>
                <input type="number" id="processing_timeout" name="processing_timeout" min="30" max="1800" value="{{ multimodal_parameters.processing_timeout }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Maximum time to wait for document processing to complete
                </p>
            </div>
        </div>
    </div>

    <!-- Vision Model Configuration -->
    <div class="config-section">
        <h2 class="config-section-header">Vision Model Configuration</h2>
        <p class="config-section-description">
            Configure vision models for image analysis and caption generation.
        </p>
        
        <!-- Local Ollama Vision Model Selection -->
        <div class="space-y-6">
            <!-- Vision Model Selection -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Local Vision Model
                    <span class="text-red-500">*</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mb-4">
                    Select from available Ollama vision models installed on your system. All processing runs locally for privacy and performance.
                </p>

                <!-- Loading State -->
                <div id="vision-models-loading" class="hidden">
                    <div class="flex items-center justify-center p-8 bg-gray-50 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600">
                        <div class="text-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Loading available vision models...</p>
                        </div>
                    </div>
                </div>

                <!-- Error State -->
                <div id="vision-models-error" class="hidden">
                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Unable to load vision models</h3>
                                <p class="mt-1 text-sm text-red-700 dark:text-red-300" id="vision-models-error-message">
                                    Could not connect to Ollama. Please ensure Ollama is running and accessible.
                                </p>
                                <div class="mt-3">
                                    <button type="button" id="retry-vision-models"
                                            class="text-sm bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200 px-3 py-1 rounded hover:bg-red-200 dark:hover:bg-red-700 transition-colors">
                                        Retry
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- No Models State -->
                <div id="vision-models-empty" class="hidden">
                    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">No vision models found</h3>
                                <p class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                                    No vision-capable models are currently installed. Install a vision model to enable multimodal features.
                                </p>
                                <div class="mt-3">
                                    <p class="text-xs text-yellow-600 dark:text-yellow-400 font-mono bg-yellow-100 dark:bg-yellow-800 p-2 rounded">
                                        ollama pull llama3.2-vision<br>
                                        ollama pull gemma3:4b-it-q4_K_M<br>
                                        ollama pull minicpm-v
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vision Models Grid -->
                <div id="vision-models-grid" class="hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="vision-models-container">
                        <!-- Vision models will be populated here -->
                    </div>
                </div>

                <!-- Hidden input to store selected vision model -->
                <input type="hidden" id="selected_vision_model" name="vision_model_name" value="{{ multimodal_parameters.vision_model_name }}">
            </div>

            <!-- Ollama Base URL -->
            <div>
                <label for="vision_model_base_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Ollama Base URL
                </label>
                <input type="url" id="vision_model_base_url" name="vision_model_base_url" value="{{ multimodal_parameters.vision_model_base_url }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    URL where your local Ollama instance is running
                </p>
            </div>

            <!-- Fallback Models -->
            <div class="md:col-span-2">
                <label for="vision_fallback_models" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Fallback Models
                </label>
                <input type="text" id="vision_fallback_models" name="vision_fallback_models" value="{{ multimodal_parameters.vision_fallback_models }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Comma-separated list of fallback vision models to try if the primary model fails
                </p>
            </div>
        </div>
    </div>

    <!-- Image Processing Configuration -->
    <div class="config-section">
        <h2 class="config-section-header">Image Processing Settings</h2>
        <p class="config-section-description">
            Configure image extraction, filtering, and vision analysis settings.
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Enable Image Extraction -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="enable_image_extraction" name="enable_image_extraction"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.enable_image_extraction %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Enable Image Extraction</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Extract images from PDF and Word documents
                </p>
            </div>

            <!-- Enable Vision Captions -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="enable_vision_captions" name="enable_vision_captions"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.enable_vision_captions %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Enable Vision Captions</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Generate descriptive captions for extracted images
                </p>
            </div>

            <!-- Max Images Per Document -->
            <div>
                <label for="max_images_per_document" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Max Images Per Document
                </label>
                <input type="number" id="max_images_per_document" name="max_images_per_document" min="1" max="200" value="{{ multimodal_parameters.max_images_per_document }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Maximum number of images to extract from a single document
                </p>
            </div>

            <!-- Min Image Size -->
            <div>
                <label for="min_image_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Min Image Size (bytes)
                </label>
                <input type="number" id="min_image_size" name="min_image_size" min="100" max="100000" value="{{ multimodal_parameters.min_image_size }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Minimum size in bytes for images to be processed (filters out small icons)
                </p>
            </div>

            <!-- Filter Logos -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="filter_logos" name="filter_logos"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.filter_logos %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Filter Logos and Icons</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Automatically filter out logos, icons, and decorative images
                </p>
            </div>

            <!-- Filter Sensitivity -->
            <div>
                <label for="filter_sensitivity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Filter Sensitivity
                </label>
                <input type="range" id="filter_sensitivity" name="filter_sensitivity" min="0" max="1" step="0.1" value="{{ multimodal_parameters.filter_sensitivity }}"
                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span>Less Filtering</span>
                    <span id="filter_sensitivity_value">0.3</span>
                    <span>More Filtering</span>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Sensitivity level for content-based image filtering
                </p>
            </div>
        </div>
    </div>

    <!-- Table Processing Configuration -->
    <div class="config-section">
        <h2 class="config-section-header">Table Processing Settings</h2>
        <p class="config-section-description">
            Configure table extraction, structure preservation, and formatting options.
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Enable Table Extraction -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="enable_table_extraction" name="enable_table_extraction"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.enable_table_extraction %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Enable Table Extraction</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Extract tables from PDF and Word documents
                </p>
            </div>

            <!-- Table Strategy -->
            <div>
                <label for="table_strategy" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Table Extraction Strategy
                </label>
                <select id="table_strategy" name="table_strategy"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                    <option value="basic" {% if multimodal_parameters.table_strategy == 'basic' %}selected{% endif %}>Basic</option>
                    <option value="advanced" {% if multimodal_parameters.table_strategy == 'advanced' %}selected{% endif %}>Advanced</option>
                    <option value="comprehensive" {% if multimodal_parameters.table_strategy == 'comprehensive' %}selected{% endif %}>Comprehensive</option>
                </select>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Strategy for table detection and extraction quality
                </p>
            </div>

            <!-- Use Camelot -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="use_camelot" name="use_camelot"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.use_camelot %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Use Camelot for Table Extraction</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Use Camelot library for high-quality table extraction
                </p>
            </div>

            <!-- Use Tabula -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="use_tabula" name="use_tabula"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.use_tabula %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Use Tabula as Fallback</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Use Tabula library as fallback when Camelot fails
                </p>
            </div>

            <!-- Max Tables Per Document -->
            <div>
                <label for="max_tables_per_document" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Max Tables Per Document
                </label>
                <input type="number" id="max_tables_per_document" name="max_tables_per_document" min="1" max="500" value="{{ multimodal_parameters.max_tables_per_document }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Maximum number of tables to extract from a single document
                </p>
            </div>

            <!-- Min Table Rows -->
            <div>
                <label for="min_table_rows" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Min Table Rows
                </label>
                <input type="number" id="min_table_rows" name="min_table_rows" min="1" max="10" value="{{ multimodal_parameters.min_table_rows }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Minimum number of rows for a table to be processed
                </p>
            </div>

            <!-- Min Table Columns -->
            <div>
                <label for="min_table_cols" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Min Table Columns
                </label>
                <input type="number" id="min_table_cols" name="min_table_cols" min="1" max="10" value="{{ multimodal_parameters.min_table_cols }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Minimum number of columns for a table to be processed
                </p>
            </div>

            <!-- Include Table Context -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="include_table_context" name="include_table_context"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.include_table_context %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Include Table Context</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Include surrounding text as context for extracted tables
                </p>
            </div>
        </div>
    </div>

    <!-- Multimodal Chunking Configuration -->
    <div class="config-section">
        <h2 class="config-section-header">Multimodal Chunking Settings</h2>
        <p class="config-section-description">
            Configure how multimodal content is chunked and organized for embedding and search.
        </p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Enable Multimodal Chunking -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="enable_multimodal_chunking" name="enable_multimodal_chunking"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.enable_multimodal_chunking %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Enable Multimodal Chunking</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Create chunks that preserve relationships between text, images, and tables
                </p>
            </div>

            <!-- Preserve Content Relationships -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="preserve_content_relationships" name="preserve_content_relationships"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.preserve_content_relationships %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Preserve Content Relationships</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Maintain spatial and contextual relationships between different content types
                </p>
            </div>

            <!-- Max Chunk Size -->
            <div>
                <label for="max_chunk_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Max Chunk Size
                </label>
                <input type="number" id="max_chunk_size" name="max_chunk_size" min="100" max="5000" value="{{ multimodal_parameters.max_chunk_size }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Maximum size in characters for text chunks
                </p>
            </div>

            <!-- Chunk Overlap -->
            <div>
                <label for="chunk_overlap" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Chunk Overlap
                </label>
                <input type="number" id="chunk_overlap" name="chunk_overlap" min="0" max="500" value="{{ multimodal_parameters.chunk_overlap }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Number of overlapping characters between adjacent chunks
                </p>
            </div>

            <!-- Image Chunk Strategy -->
            <div>
                <label for="image_chunk_strategy" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Image Chunk Strategy
                </label>
                <select id="image_chunk_strategy" name="image_chunk_strategy"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                    <option value="separate" {% if multimodal_parameters.image_chunk_strategy == 'separate' %}selected{% endif %}>Separate Chunks</option>
                    <option value="inline" {% if multimodal_parameters.image_chunk_strategy == 'inline' %}selected{% endif %}>Inline with Text</option>
                    <option value="reference" {% if multimodal_parameters.image_chunk_strategy == 'reference' %}selected{% endif %}>Reference Only</option>
                </select>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    How to handle images when creating chunks
                </p>
            </div>

            <!-- Table Chunk Strategy -->
            <div>
                <label for="table_chunk_strategy" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Table Chunk Strategy
                </label>
                <select id="table_chunk_strategy" name="table_chunk_strategy"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                    <option value="preserve" {% if multimodal_parameters.table_chunk_strategy == 'preserve' %}selected{% endif %}>Preserve Structure</option>
                    <option value="split" {% if multimodal_parameters.table_chunk_strategy == 'split' %}selected{% endif %}>Split Large Tables</option>
                    <option value="reference" {% if multimodal_parameters.table_chunk_strategy == 'reference' %}selected{% endif %}>Reference Only</option>
                </select>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    How to handle tables when creating chunks
                </p>
            </div>
        </div>
    </div>

    <!-- Hybrid Search Configuration -->
    <div class="config-section">
        <h2 class="config-section-header">Hybrid Search Settings</h2>
        <p class="config-section-description">
            Configure hybrid search across text, images, and tables with content type weighting.
        </p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Enable Hybrid Search -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="enable_hybrid_search" name="enable_hybrid_search"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.enable_hybrid_search %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Enable Hybrid Search</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Search across text, images, and tables with intelligent ranking
                </p>
            </div>

            <!-- Separate Collections -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="separate_collections" name="separate_collections"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.separate_collections %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Use Separate Collections</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Store different content types in separate vector database collections
                </p>
            </div>

            <!-- Content Type Weights -->
            <div class="md:col-span-2">
                <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-3">Content Type Weights</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Text Weight -->
                    <div>
                        <label for="text_weight" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Text Weight
                        </label>
                        <input type="range" id="text_weight" name="text_weight" min="0" max="1" step="0.1" value="{{ multimodal_parameters.text_weight }}"
                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                            <span>0.0</span>
                            <span id="text_weight_value">0.6</span>
                            <span>1.0</span>
                        </div>
                    </div>

                    <!-- Image Weight -->
                    <div>
                        <label for="image_weight" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Image Weight
                        </label>
                        <input type="range" id="image_weight" name="image_weight" min="0" max="1" step="0.1" value="{{ multimodal_parameters.image_weight }}"
                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                            <span>0.0</span>
                            <span id="image_weight_value">0.2</span>
                            <span>1.0</span>
                        </div>
                    </div>

                    <!-- Table Weight -->
                    <div>
                        <label for="table_weight" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Table Weight
                        </label>
                        <input type="range" id="table_weight" name="table_weight" min="0" max="1" step="0.1" value="{{ multimodal_parameters.table_weight }}"
                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                            <span>0.0</span>
                            <span id="table_weight_value">0.2</span>
                            <span>1.0</span>
                        </div>
                    </div>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    Relative importance of different content types in search results (should sum to 1.0)
                </p>
            </div>

            <!-- Max Results Per Type -->
            <div class="md:col-span-2">
                <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-3">Max Results Per Content Type</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Max Text Results -->
                    <div>
                        <label for="max_text_results" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Max Text Results
                        </label>
                        <input type="number" id="max_text_results" name="max_text_results" min="1" max="50" value="{{ multimodal_parameters.max_text_results }}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                    </div>

                    <!-- Max Image Results -->
                    <div>
                        <label for="max_image_results" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Max Image Results
                        </label>
                        <input type="number" id="max_image_results" name="max_image_results" min="1" max="20" value="{{ multimodal_parameters.max_image_results }}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                    </div>

                    <!-- Max Table Results -->
                    <div>
                        <label for="max_table_results" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Max Table Results
                        </label>
                        <input type="number" id="max_table_results" name="max_table_results" min="1" max="20" value="{{ multimodal_parameters.max_table_results }}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                    </div>
                </div>
            </div>

            <!-- Similarity Threshold -->
            <div>
                <label for="similarity_threshold" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Similarity Threshold
                </label>
                <input type="range" id="similarity_threshold" name="similarity_threshold" min="0" max="1" step="0.05" value="{{ multimodal_parameters.similarity_threshold }}"
                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span>0.0</span>
                    <span id="similarity_threshold_value">0.7</span>
                    <span>1.0</span>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Minimum similarity score for results to be included
                </p>
            </div>

            <!-- Rerank Results -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="rerank_results" name="rerank_results"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.rerank_results %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Rerank Results</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Apply intelligent reranking to improve result quality
                </p>
            </div>
        </div>
    </div>

    <!-- Storage Configuration -->
    <div class="config-section">
        <h2 class="config-section-header">Storage Settings</h2>
        <p class="config-section-description">
            Configure BLOB storage for images and metadata caching options.
        </p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Enable BLOB Storage -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="enable_blob_storage" name="enable_blob_storage"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.enable_blob_storage %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Enable BLOB Storage</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Store images and large content as binary objects
                </p>
            </div>

            <!-- Compress Images -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="compress_images" name="compress_images"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.compress_images %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Compress Images</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Compress images to save storage space
                </p>
            </div>

            <!-- BLOB Storage Path Pattern -->
            <div>
                <label for="blob_storage_path" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    BLOB Storage Path Pattern
                    <span class="text-red-500">*</span>
                </label>
                <input type="text" id="blob_storage_path" name="blob_storage_path" value="{{ multimodal_parameters.blob_storage_path }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300"
                       placeholder="temp/{category}/{filename}/multimodal_blobs">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Directory pattern for storing BLOB files. Use <code>{category}</code> and <code>{filename}</code> placeholders for document-specific storage.
                </p>
                <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                    <strong>Example:</strong> <code>temp/{category}/{filename}/multimodal_blobs</code> creates nested storage within each document's directory for automatic cleanup.
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    This co-locates BLOB files with their parent documents, ensuring automatic deletion when documents are removed.
                </p>
            </div>

            <!-- Max BLOB Size -->
            <div>
                <label for="max_blob_size_mb" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Max BLOB Size (MB)
                </label>
                <input type="number" id="max_blob_size_mb" name="max_blob_size_mb" min="1" max="500" value="{{ multimodal_parameters.max_blob_size_mb }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Maximum size in MB for individual BLOB files
                </p>
            </div>

            <!-- Image Quality -->
            <div>
                <label for="image_quality" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Image Quality (%)
                </label>
                <input type="range" id="image_quality" name="image_quality" min="10" max="100" step="5" value="{{ multimodal_parameters.image_quality }}"
                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span>10%</span>
                    <span id="image_quality_value">85%</span>
                    <span>100%</span>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    JPEG quality for compressed images (higher = better quality, larger size)
                </p>
            </div>

            <!-- Cache TTL -->
            <div>
                <label for="metadata_cache_ttl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Metadata Cache TTL (seconds)
                </label>
                <input type="number" id="metadata_cache_ttl" name="metadata_cache_ttl" min="300" max="86400" value="{{ multimodal_parameters.metadata_cache_ttl }}"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Time-to-live for metadata cache entries (3600 = 1 hour)
                </p>
            </div>
        </div>
    </div>

    <!-- Performance Monitoring -->
    <div class="config-section">
        <h2 class="config-section-header">Performance Monitoring</h2>
        <p class="config-section-description">
            Configure performance monitoring and debugging options for multimodal operations.
        </p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Enable Performance Monitoring -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="enable_performance_monitoring" name="enable_performance_monitoring"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.enable_performance_monitoring %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Enable Performance Monitoring</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Track processing times, memory usage, and operation metrics
                </p>
            </div>

            <!-- Debug Mode -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="debug_mode" name="debug_mode"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.debug_mode %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Debug Mode</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Enable detailed logging and debugging information
                </p>
            </div>

            <!-- Log Processing Steps -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" id="log_processing_steps" name="log_processing_steps"
                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                           {% if multimodal_parameters.log_processing_steps %}checked{% endif %}>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Log Processing Steps</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Log detailed information about each processing step
                </p>
            </div>
        </div>
    </div>

    <!-- Save Button for Multimodal Tab -->
    <div class="mt-8 flex justify-end">
        <button type="button" id="saveMultimodalBtn"
                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
            Save Multimodal Settings
        </button>
    </div>
</div>

<!-- JavaScript for dynamic updates -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update range slider values
    const rangeInputs = [
        'filter_sensitivity',
        'text_weight',
        'image_weight',
        'table_weight',
        'similarity_threshold',
        'image_quality'
    ];

    rangeInputs.forEach(function(inputId) {
        const input = document.getElementById(inputId);
        const valueDisplay = document.getElementById(inputId + '_value');

        if (input && valueDisplay) {
            input.addEventListener('input', function() {
                let value = this.value;
                if (inputId === 'image_quality') {
                    value += '%';
                }
                valueDisplay.textContent = value;
            });
        }
    });

    // Vision model loading and selection
    let selectedVisionModel = '{{ multimodal_parameters.vision_model_name }}'; // Load from configuration
    let availableVisionModels = [];

    // Load available vision models on page load
    loadAvailableVisionModels();

    // Retry button handler
    const retryButton = document.getElementById('retry-vision-models');
    if (retryButton) {
        retryButton.addEventListener('click', loadAvailableVisionModels);
    }

    async function loadAvailableVisionModels() {
        const loadingDiv = document.getElementById('vision-models-loading');
        const errorDiv = document.getElementById('vision-models-error');
        const emptyDiv = document.getElementById('vision-models-empty');
        const gridDiv = document.getElementById('vision-models-grid');

        // Show loading state
        showVisionModelState('loading');

        try {
            const response = await fetch('/api/vision_models');
            const visionModels = await response.json();

            if (!response.ok) {
                throw new Error(visionModels.error || 'Failed to fetch vision models');
            }

            if (!visionModels || visionModels.length === 0) {
                showVisionModelState('empty');
                return;
            }

            availableVisionModels = visionModels;
            populateVisionModels(visionModels);
            showVisionModelState('success');

        } catch (error) {
            console.error('Error loading vision models:', error);
            const errorMessage = document.getElementById('vision-models-error-message');
            if (errorMessage) {
                errorMessage.textContent = error.message;
            }
            showVisionModelState('error');
        }
    }

    function showVisionModelState(state) {
        const states = ['loading', 'error', 'empty', 'success'];
        states.forEach(s => {
            const element = document.getElementById(`vision-models-${s === 'success' ? 'grid' : s}`);
            if (element) {
                element.classList.toggle('hidden', s !== state);
            }
        });
    }

    function populateVisionModels(models) {
        const container = document.getElementById('vision-models-container');
        if (!container) return;

        container.innerHTML = '';

        models.forEach((model, index) => {
            const modelDiv = document.createElement('div');
            modelDiv.className = 'relative';

            const isSelected = model.name === selectedVisionModel;
            const isDefault = model.name === 'gemma3:4b-it-q4_K_M'; // User's current model

            modelDiv.innerHTML = `
                <input type="radio" id="vision_model_${index}" name="vision_model_name" value="${model.name}"
                       class="hidden peer" ${isSelected ? 'checked' : ''}>
                <label for="vision_model_${index}"
                       class="block p-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer
                              ${isSelected ? 'border-blue-500 ring-2 ring-blue-500' : ''}
                              peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                              hover:bg-gray-50 dark:hover:bg-gray-600 transition-all
                              ${isDefault ? 'border-green-200 dark:border-green-600' : ''}">
                    <div class="font-medium text-gray-900 dark:text-gray-100">${model.name}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Size: ${formatModelSize(model.size)}
                    </div>
                    ${isDefault ? '<div class="text-xs text-green-600 dark:text-green-400 mt-1 font-semibold">Current Model</div>' : ''}
                </label>
            `;

            container.appendChild(modelDiv);

            // Add click handler
            const radio = modelDiv.querySelector('input[type="radio"]');
            radio.addEventListener('change', function() {
                if (this.checked) {
                    selectedVisionModel = this.value;
                    updateVisionModelSelection();
                }
            });
        });
    }

    function updateVisionModelSelection() {
        // Update any hidden inputs or form fields that need the selected model
        const hiddenInput = document.getElementById('selected_vision_model');
        if (hiddenInput) {
            hiddenInput.value = selectedVisionModel;
        }
    }

    function formatModelSize(bytes) {
        if (!bytes) return 'Unknown';
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    // Weight normalization
    const weightInputs = ['text_weight', 'image_weight', 'table_weight'];
    weightInputs.forEach(function(inputId) {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('change', function() {
                normalizeWeights();
            });
        }
    });

    function normalizeWeights() {
        const textWeight = parseFloat(document.getElementById('text_weight').value);
        const imageWeight = parseFloat(document.getElementById('image_weight').value);
        const tableWeight = parseFloat(document.getElementById('table_weight').value);

        const total = textWeight + imageWeight + tableWeight;

        if (total > 1.0) {
            // Show warning
            console.warn('Content type weights sum to more than 1.0. Consider adjusting values.');
        }
    }
});
</script>
