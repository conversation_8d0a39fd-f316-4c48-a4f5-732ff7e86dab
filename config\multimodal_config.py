"""
Multimodal RAG Pipeline Configuration
Configuration for multimodal content extraction and processing
"""

import os
from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from enum import Enum

class VisionModelType(Enum):
    """Supported vision model types - Local Ollama only"""
    OLLAMA_LOCAL = "ollama_local"

class ContentType(Enum):
    """Supported content types for multimodal processing"""
    TEXT = "text"
    IMAGE = "image"
    TABLE = "table"
    MIXED = "mixed"

@dataclass
class VisionModelConfig:
    """Configuration for vision models - Local Ollama only"""
    model_type: VisionModelType = VisionModelType.OLLAMA_LOCAL
    model_name: str = "gemma3:4b-it-q4_K_M"  # Default to user's current vision model
    api_key: Optional[str] = None  # Not used for local models
    base_url: str = "http://localhost:11434"  # Default Ollama URL
    max_retries: int = 3
    timeout: int = 30
    fallback_models: List[str] = None
    
    def __post_init__(self):
        if self.fallback_models is None:
            self.fallback_models = ["minicpm-v", "llava"]

@dataclass
class ImageProcessingConfig:
    """Configuration for image processing"""
    enable_image_extraction: bool = True
    enable_vision_captions: bool = True
    max_images_per_document: int = 50
    min_image_size: int = 1024  # Minimum image size in bytes
    supported_formats: List[str] = None
    filter_logos: bool = True
    filter_sensitivity: float = 0.3
    dpi: int = 300
    vision_prompt_template: str = "Describe this image in detail, focusing on any text, diagrams, charts, or important visual elements that would be relevant for document search and retrieval."
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ["png", "jpg", "jpeg", "gif", "bmp"]

@dataclass
class TableProcessingConfig:
    """Configuration for table processing"""
    enable_table_extraction: bool = True
    table_strategy: str = "advanced"  # PyMuPDF table strategy
    use_camelot: bool = True
    use_tabula: bool = True  # Fallback if camelot fails
    max_tables_per_document: int = 100
    min_table_rows: int = 2
    min_table_cols: int = 2
    preserve_table_structure: bool = True
    table_to_markdown: bool = True
    include_table_context: bool = True  # Include surrounding text
    context_window_chars: int = 500  # Characters before/after table
    
@dataclass
class ChunkingConfig:
    """Configuration for multimodal chunking"""
    enable_multimodal_chunking: bool = True
    preserve_content_relationships: bool = True
    max_chunk_size: int = 1000
    chunk_overlap: int = 200
    image_chunk_strategy: str = "separate"  # "separate", "inline", "reference"
    table_chunk_strategy: str = "preserve"  # "preserve", "split", "reference"
    include_spatial_metadata: bool = True
    
@dataclass
class EmbeddingConfig:
    """Configuration for multimodal embeddings"""
    text_embedding_model: str = "nomic-embed-text"
    image_description_embedding: bool = True
    table_content_embedding: bool = True
    separate_collections: bool = True  # Separate ChromaDB collections per content type
    embedding_cache_ttl: int = 86400  # 24 hours
    batch_size: int = 10
    
@dataclass
class SearchConfig:
    """Configuration for hybrid search"""
    enable_hybrid_search: bool = True
    content_type_weights: Dict[str, float] = None
    max_results_per_type: Dict[str, int] = None
    similarity_threshold: float = 0.7
    rerank_results: bool = True
    include_content_in_results: bool = True
    
    def __post_init__(self):
        if self.content_type_weights is None:
            self.content_type_weights = {
                "text": 0.6,
                "image": 0.2,
                "table": 0.2
            }
        if self.max_results_per_type is None:
            self.max_results_per_type = {
                "text": 10,
                "image": 5,
                "table": 5
            }

@dataclass
class StorageConfig:
    """Configuration for multimodal storage"""
    enable_blob_storage: bool = True
    blob_storage_path: str = "temp/{category}/{filename}/multimodal_blobs"
    max_blob_size_mb: int = 50
    compress_images: bool = True
    image_quality: int = 85  # JPEG quality for compressed images
    store_original_images: bool = True
    metadata_cache_ttl: int = 3600  # 1 hour
    
@dataclass
class MultimodalRAGConfig:
    """Main configuration class for multimodal RAG pipeline"""
    
    # Core settings
    enable_multimodal: bool = True
    supported_document_formats: List[str] = None
    
    # Component configurations
    vision_model: VisionModelConfig = None
    image_processing: ImageProcessingConfig = None
    table_processing: TableProcessingConfig = None
    chunking: ChunkingConfig = None
    embedding: EmbeddingConfig = None
    search: SearchConfig = None
    storage: StorageConfig = None
    
    # Performance settings
    enable_parallel_processing: bool = True
    max_workers: int = 4
    processing_timeout: int = 300  # 5 minutes
    
    # Monitoring and debugging
    enable_performance_monitoring: bool = True
    debug_mode: bool = False
    log_processing_steps: bool = True
    
    def __post_init__(self):
        if self.supported_document_formats is None:
            self.supported_document_formats = ["pdf", "docx", "doc"]
            
        # Initialize component configs if not provided
        if self.vision_model is None:
            self.vision_model = VisionModelConfig()
        if self.image_processing is None:
            self.image_processing = ImageProcessingConfig()
        if self.table_processing is None:
            self.table_processing = TableProcessingConfig()
        if self.chunking is None:
            self.chunking = ChunkingConfig()
        if self.embedding is None:
            self.embedding = EmbeddingConfig()
        if self.search is None:
            self.search = SearchConfig()
        if self.storage is None:
            self.storage = StorageConfig()

# Global configuration instance
_multimodal_config = None

def get_multimodal_config() -> MultimodalRAGConfig:
    """Get the global multimodal configuration instance"""
    global _multimodal_config
    if _multimodal_config is None:
        _multimodal_config = MultimodalRAGConfig()
    return _multimodal_config

def set_multimodal_config(config: MultimodalRAGConfig):
    """Set the global multimodal configuration instance"""
    global _multimodal_config
    _multimodal_config = config

def load_multimodal_config_from_env() -> MultimodalRAGConfig:
    """Load multimodal configuration from environment variables"""
    config = MultimodalRAGConfig()
    
    # Vision model settings
    if os.getenv("MULTIMODAL_VISION_MODEL"):
        config.vision_model.model_name = os.getenv("MULTIMODAL_VISION_MODEL")
    if os.getenv("MULTIMODAL_VISION_API_KEY"):
        config.vision_model.api_key = os.getenv("MULTIMODAL_VISION_API_KEY")
    if os.getenv("MULTIMODAL_VISION_BASE_URL"):
        config.vision_model.base_url = os.getenv("MULTIMODAL_VISION_BASE_URL")
    
    # Processing settings
    if os.getenv("MULTIMODAL_ENABLE_IMAGES"):
        config.image_processing.enable_image_extraction = os.getenv("MULTIMODAL_ENABLE_IMAGES").lower() == "true"
    if os.getenv("MULTIMODAL_ENABLE_TABLES"):
        config.table_processing.enable_table_extraction = os.getenv("MULTIMODAL_ENABLE_TABLES").lower() == "true"
    if os.getenv("MULTIMODAL_MAX_WORKERS"):
        config.max_workers = int(os.getenv("MULTIMODAL_MAX_WORKERS"))
    
    # Storage settings
    if os.getenv("MULTIMODAL_BLOB_STORAGE_PATH"):
        config.storage.blob_storage_path = os.getenv("MULTIMODAL_BLOB_STORAGE_PATH")
    
    return config

# Initialize default configuration
_multimodal_config = load_multimodal_config_from_env()
