import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_temp_directories():
    """Create the _temp directory structure."""
    try:
        # Define the base directory to create
        temp_dir = "./data/temp"

        # Create the base directory
        os.makedirs(temp_dir, exist_ok=True)
        logger.info(f"Created base directory: {temp_dir}")

        return True
    except Exception as e:
        logger.error(f"Failed to create temporary directories: {str(e)}")
        return False

def create_pdf_directory_structure(category, pdf_name):
    """
    Create the hierarchical directory structure for a specific PDF.

    Args:
        category: The category name
        pdf_name: The PDF filename (with extension)

    Returns:
        dict: A dictionary containing the paths to the created directories
    """
    try:
        # Remove the .pdf extension if present
        pdf_base_name = os.path.splitext(pdf_name)[0]

        # Define the directories to create
        temp_dir = "./data/temp"
        category_dir = os.path.join(temp_dir, category)
        pdf_dir = os.path.join(category_dir, pdf_base_name)
        pdf_images_dir = os.path.join(pdf_dir, "pdf_images")
        pdf_tables_dir = os.path.join(pdf_dir, "pdf_tables")
        pdf_text_dir = os.path.join(pdf_dir, "pdf_text")
        cover_image_dir = os.path.join(pdf_images_dir, "cover_image")

        # Multimodal BLOB storage directories
        multimodal_blobs_dir = os.path.join(pdf_dir, "multimodal_blobs")
        multimodal_images_dir = os.path.join(multimodal_blobs_dir, "images")
        multimodal_tables_dir = os.path.join(multimodal_blobs_dir, "tables")
        multimodal_metadata_dir = os.path.join(multimodal_blobs_dir, "metadata")

        # Create the directories with detailed logging
        logger.info(f"Creating directory structure for {pdf_name} in category {category}")
        logger.info(f"Base temp directory: {temp_dir}")
        logger.info(f"Category directory: {category_dir}")
        logger.info(f"PDF directory: {pdf_dir}")
        
        # Create the directories
        os.makedirs(category_dir, exist_ok=True)
        logger.info(f"Created category directory: {category_dir}")

        os.makedirs(pdf_dir, exist_ok=True)
        logger.info(f"Created PDF directory: {pdf_dir}")

        os.makedirs(pdf_images_dir, exist_ok=True)
        os.makedirs(pdf_tables_dir, exist_ok=True)
        os.makedirs(pdf_text_dir, exist_ok=True)
        os.makedirs(cover_image_dir, exist_ok=True)

        # Create multimodal BLOB storage directories
        os.makedirs(multimodal_blobs_dir, exist_ok=True)
        os.makedirs(multimodal_images_dir, exist_ok=True)
        os.makedirs(multimodal_tables_dir, exist_ok=True)
        os.makedirs(multimodal_metadata_dir, exist_ok=True)
        logger.info(f"Created multimodal BLOB storage directories in: {multimodal_blobs_dir}")
        
        # Verify directories were created
        pdf_path = os.path.join(pdf_dir, pdf_name)
        logger.info(f"Expected PDF path: {pdf_path}")
        logger.info(f"PDF directory exists: {os.path.exists(pdf_dir)}")
        logger.info(f"Category directory exists: {os.path.exists(category_dir)}")

        # Check if a category placeholder image exists, create one if it doesn't
        category_placeholder = os.path.join(category_dir, "placeholder.jpg")
        if not os.path.exists(category_placeholder):
            try:
                # Create a placeholder image for the category
                from PIL import Image, ImageDraw, ImageFont

                # Create a blank image with a gradient background
                img = Image.new('RGB', (400, 300), color=(240, 240, 240))
                d = ImageDraw.Draw(img)

                # Try to use a system font
                try:
                    font = ImageFont.truetype("arial.ttf", 24)
                    small_font = ImageFont.truetype("arial.ttf", 16)
                except IOError:
                    try:
                        # Try another common font on different systems
                        font = ImageFont.truetype("DejaVuSans.ttf", 24)
                        small_font = ImageFont.truetype("DejaVuSans.ttf", 16)
                    except IOError:
                        font = ImageFont.load_default()
                        small_font = ImageFont.load_default()

                # Add category name to the image
                text = f"Category: {category}"
                text_width = d.textlength(text, font=font)
                d.text(((400-text_width)/2, 120), text, fill=(0, 0, 0), font=font)

                # Add explanatory text
                subtext = "Default Category Placeholder"
                subtext_width = d.textlength(subtext, font=small_font)
                d.text(((400-subtext_width)/2, 160), subtext, fill=(100, 100, 100), font=small_font)

                # Save the image
                img.save(category_placeholder)
                logger.info(f"Created category placeholder image: {category_placeholder}")
            except Exception as placeholder_error:
                logger.error(f"Failed to create category placeholder image: {str(placeholder_error)}")

        logger.info(f"Created PDF directory structure for {pdf_name} in category {category}")

        # Return the paths for use in other functions
        return {
            "category_dir": category_dir,
            "pdf_dir": pdf_dir,
            "pdf_images_dir": pdf_images_dir,
            "pdf_tables_dir": pdf_tables_dir,
            "pdf_text_dir": pdf_text_dir,
            "cover_image_dir": cover_image_dir,
            "multimodal_blobs_dir": multimodal_blobs_dir,
            "multimodal_images_dir": multimodal_images_dir,
            "multimodal_tables_dir": multimodal_tables_dir,
            "multimodal_metadata_dir": multimodal_metadata_dir,
            "pdf_path": os.path.join(pdf_dir, pdf_name)
        }
    except Exception as e:
        logger.error(f"Failed to create PDF directory structure for {pdf_name} in {category}: {str(e)}")
        return None

if __name__ == "__main__":
    create_temp_directories()
