"""
Multimodal Storage Service
Handles storage and retrieval of multimodal content including images, tables, and metadata
"""

import os
import json
import hashlib
import logging
from typing import Dict, Any, Optional, List, Union, BinaryIO
from datetime import datetime
from pathlib import Path
import base64
from PIL import Image
import io

from config.multimodal_config import get_multimodal_config, ContentType
from app.services.cache_service import cache_service

logger = logging.getLogger(__name__)

class MultimodalStorage:
    """Storage service for multimodal content"""

    def __init__(self, config=None):
        self.config = config or get_multimodal_config()
        self.blob_storage_pattern = self.config.storage.blob_storage_path

        # For backward compatibility, check if pattern contains placeholders
        self.uses_document_specific_paths = "{category}" in self.blob_storage_pattern and "{filename}" in self.blob_storage_pattern

        if not self.uses_document_specific_paths:
            # Legacy global storage path
            self.blob_storage_path = Path(self.config.storage.blob_storage_path)
            self.blob_storage_path.mkdir(parents=True, exist_ok=True)

            # Create subdirectories for different content types
            self.image_storage_path = self.blob_storage_path / "images"
            self.table_storage_path = self.blob_storage_path / "tables"
            self.metadata_storage_path = self.blob_storage_path / "metadata"

            for path in [self.image_storage_path, self.table_storage_path, self.metadata_storage_path]:
                path.mkdir(parents=True, exist_ok=True)

    def get_document_storage_path(self, category: str, filename: str) -> Path:
        """
        Generate document-specific storage path using the configured pattern.

        Args:
            category: Document category
            filename: Document filename (with or without extension)

        Returns:
            Path object for the document's multimodal storage directory
        """
        if not self.uses_document_specific_paths:
            # Fallback to legacy global storage
            return self.blob_storage_path

        # Remove extension from filename for directory name
        pdf_base_name = os.path.splitext(filename)[0]

        # Handle prefixes (ocr_, non_ocr_) for directory naming consistency
        if pdf_base_name.startswith('non_ocr_'):
            pdf_dir_name = pdf_base_name[8:]  # Remove 'non_ocr_' prefix
        elif pdf_base_name.startswith('ocr_'):
            pdf_dir_name = pdf_base_name[4:]  # Remove 'ocr_' prefix
        else:
            pdf_dir_name = pdf_base_name

        # Replace placeholders in the pattern
        storage_path = self.blob_storage_pattern.format(
            category=category,
            filename=pdf_dir_name
        )

        return Path(storage_path)

    def _get_storage_paths(self, category: str, filename: str):
        """
        Get document-specific storage paths for images, tables, and metadata.
        Creates directories if they don't exist.

        Args:
            category: Document category
            filename: Document filename

        Returns:
            Tuple of (image_path, table_path, metadata_path)
        """
        if not self.uses_document_specific_paths:
            # Use legacy global paths
            return self.image_storage_path, self.table_storage_path, self.metadata_storage_path

        base_path = self.get_document_storage_path(category, filename)
        base_path.mkdir(parents=True, exist_ok=True)

        image_path = base_path / "images"
        table_path = base_path / "tables"
        metadata_path = base_path / "metadata"

        # Create subdirectories
        for path in [image_path, table_path, metadata_path]:
            path.mkdir(parents=True, exist_ok=True)

        return image_path, table_path, metadata_path

    def _generate_content_hash(self, content: Union[bytes, str]) -> str:
        """Generate a hash for content to use as storage key"""
        if isinstance(content, str):
            content = content.encode('utf-8')
        return hashlib.sha256(content).hexdigest()
    
    def _get_cache_key(self, content_type: ContentType, content_hash: str) -> str:
        """Generate cache key for metadata"""
        return f"multimodal:{content_type.value}:{content_hash}"
    
    def store_image(self, image_data: bytes, metadata: Dict[str, Any],
                   document_id: str, page_num: int = None, category: str = None, filename: str = None) -> str:
        """
        Store image data and metadata

        Args:
            image_data: Raw image bytes
            metadata: Image metadata including format, size, etc.
            document_id: ID of the source document
            page_num: Page number where image was found
            category: Document category (for document-specific storage)
            filename: Document filename (for document-specific storage)

        Returns:
            Storage ID for the image
        """
        try:
            # Generate content hash
            content_hash = self._generate_content_hash(image_data)

            # Get appropriate storage paths
            if self.uses_document_specific_paths and category and filename:
                image_storage_path, _, metadata_storage_path = self._get_storage_paths(category, filename)
            else:
                # Fallback to legacy global storage
                image_storage_path = self.image_storage_path
                metadata_storage_path = self.metadata_storage_path

            # Check if image already exists
            image_path = image_storage_path / f"{content_hash}.blob"
            if image_path.exists():
                logger.debug(f"Image already exists: {content_hash}")
                return content_hash

            # Compress image if enabled
            if self.config.storage.compress_images:
                image_data = self._compress_image(image_data)

            # Check size limit
            size_mb = len(image_data) / (1024 * 1024)
            if size_mb > self.config.storage.max_blob_size_mb:
                logger.warning(f"Image size {size_mb:.2f}MB exceeds limit {self.config.storage.max_blob_size_mb}MB")
                return None

            # Store image data
            with open(image_path, 'wb') as f:
                f.write(image_data)

            # Prepare metadata
            full_metadata = {
                "content_type": ContentType.IMAGE.value,
                "content_hash": content_hash,
                "document_id": document_id,
                "page_num": page_num,
                "storage_path": str(image_path),
                "size_bytes": len(image_data),
                "created_at": datetime.now().isoformat(),
                "category": category,
                "filename": filename,
                **metadata
            }

            # Store metadata using appropriate path
            self._store_metadata_at_path(content_hash, full_metadata, metadata_storage_path)

            logger.info(f"Stored image: {content_hash} ({size_mb:.2f}MB) at {image_path}")
            return content_hash

        except Exception as e:
            logger.error(f"Error storing image: {e}")
            return None
    
    def store_table(self, table_data: Dict[str, Any], metadata: Dict[str, Any],
                   document_id: str, page_num: int = None, category: str = None, filename: str = None) -> str:
        """
        Store table data and metadata

        Args:
            table_data: Table content (markdown, CSV, structured data)
            metadata: Table metadata including structure info
            document_id: ID of the source document
            page_num: Page number where table was found
            category: Document category (for document-specific storage)
            filename: Document filename (for document-specific storage)

        Returns:
            Storage ID for the table
        """
        try:
            # Serialize table data
            table_json = json.dumps(table_data, ensure_ascii=False)
            content_hash = self._generate_content_hash(table_json)

            # Get appropriate storage paths
            if self.uses_document_specific_paths and category and filename:
                _, table_storage_path, metadata_storage_path = self._get_storage_paths(category, filename)
            else:
                # Fallback to legacy global storage
                table_storage_path = self.table_storage_path
                metadata_storage_path = self.metadata_storage_path

            # Check if table already exists
            table_path = table_storage_path / f"{content_hash}.json"
            if table_path.exists():
                logger.debug(f"Table already exists: {content_hash}")
                return content_hash

            # Store table data
            with open(table_path, 'w', encoding='utf-8') as f:
                f.write(table_json)

            # Prepare metadata
            full_metadata = {
                "content_type": ContentType.TABLE.value,
                "content_hash": content_hash,
                "document_id": document_id,
                "page_num": page_num,
                "storage_path": str(table_path),
                "size_bytes": len(table_json.encode('utf-8')),
                "created_at": datetime.now().isoformat(),
                "category": category,
                "filename": filename,
                **metadata
            }
            
            # Store metadata using appropriate path
            self._store_metadata_at_path(content_hash, full_metadata, metadata_storage_path)

            logger.info(f"Stored table: {content_hash} at {table_path}")
            return content_hash
            
        except Exception as e:
            logger.error(f"Error storing table: {e}")
            return None
    
    def retrieve_image(self, content_hash: str) -> Optional[Dict[str, Any]]:
        """Retrieve image data and metadata"""
        try:
            # Get metadata first
            metadata = self.get_metadata(content_hash)
            if not metadata or metadata.get("content_type") != ContentType.IMAGE.value:
                return None
            
            # Read image data
            image_path = Path(metadata["storage_path"])
            if not image_path.exists():
                logger.error(f"Image file not found: {image_path}")
                return None
            
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            return {
                "data": image_data,
                "metadata": metadata
            }
            
        except Exception as e:
            logger.error(f"Error retrieving image {content_hash}: {e}")
            return None
    
    def retrieve_table(self, content_hash: str) -> Optional[Dict[str, Any]]:
        """Retrieve table data and metadata"""
        try:
            # Get metadata first
            metadata = self.get_metadata(content_hash)
            if not metadata or metadata.get("content_type") != ContentType.TABLE.value:
                return None
            
            # Read table data
            table_path = Path(metadata["storage_path"])
            if not table_path.exists():
                logger.error(f"Table file not found: {table_path}")
                return None
            
            with open(table_path, 'r', encoding='utf-8') as f:
                table_data = json.load(f)
            
            return {
                "data": table_data,
                "metadata": metadata
            }
            
        except Exception as e:
            logger.error(f"Error retrieving table {content_hash}: {e}")
            return None
    
    def get_metadata(self, content_hash: str, category: str = None, filename: str = None) -> Optional[Dict[str, Any]]:
        """Get metadata for content, searching both document-specific and legacy locations"""
        try:
            # Try cache first
            cache_key = f"multimodal:metadata:{content_hash}"
            cached_metadata = cache_service.get(cache_key)
            if cached_metadata:
                return cached_metadata

            metadata = None

            # Try document-specific location first if we have the context
            if self.uses_document_specific_paths and category and filename:
                try:
                    _, _, metadata_storage_path = self._get_storage_paths(category, filename)
                    metadata_path = metadata_storage_path / f"{content_hash}.json"
                    if metadata_path.exists():
                        with open(metadata_path, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                except Exception as e:
                    logger.debug(f"Could not read from document-specific location: {e}")

            # Fallback to legacy global location
            if not metadata and hasattr(self, 'metadata_storage_path'):
                metadata_path = self.metadata_storage_path / f"{content_hash}.json"
                if metadata_path.exists():
                    with open(metadata_path, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)

            if not metadata:
                return None

            # Cache metadata
            cache_service.set(cache_key, metadata, self.config.storage.metadata_cache_ttl)

            return metadata

        except Exception as e:
            logger.error(f"Error getting metadata for {content_hash}: {e}")
            return None
    
    def _store_metadata(self, content_hash: str, metadata: Dict[str, Any]):
        """Store metadata for content using legacy global path"""
        try:
            metadata_path = self.metadata_storage_path / f"{content_hash}.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

            # Cache metadata
            cache_key = f"multimodal:metadata:{content_hash}"
            cache_service.set(cache_key, metadata, self.config.storage.metadata_cache_ttl)

        except Exception as e:
            logger.error(f"Error storing metadata for {content_hash}: {e}")

    def _store_metadata_at_path(self, content_hash: str, metadata: Dict[str, Any], metadata_storage_path: Path):
        """Store metadata for content at specified path"""
        try:
            metadata_path = metadata_storage_path / f"{content_hash}.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

            # Cache metadata
            cache_key = f"multimodal:metadata:{content_hash}"
            cache_service.set(cache_key, metadata, self.config.storage.metadata_cache_ttl)

        except Exception as e:
            logger.error(f"Error storing metadata for {content_hash} at {metadata_storage_path}: {e}")

    def _compress_image(self, image_data: bytes) -> bytes:
        """Compress image data"""
        try:
            # Open image
            image = Image.open(io.BytesIO(image_data))
            
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA', 'P'):
                image = image.convert('RGB')
            
            # Compress
            output = io.BytesIO()
            image.save(output, format='JPEG', quality=self.config.storage.image_quality, optimize=True)
            
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error compressing image: {e}")
            return image_data  # Return original if compression fails
    
    def list_content_by_document(self, document_id: str, 
                                content_type: Optional[ContentType] = None) -> List[Dict[str, Any]]:
        """List all content for a specific document"""
        try:
            content_list = []
            
            # Search through metadata files
            for metadata_file in self.metadata_storage_path.glob("*.json"):
                try:
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                    
                    if metadata.get("document_id") == document_id:
                        if content_type is None or metadata.get("content_type") == content_type.value:
                            content_list.append(metadata)
                            
                except Exception as e:
                    logger.error(f"Error reading metadata file {metadata_file}: {e}")
                    continue
            
            # Sort by page number and creation time
            content_list.sort(key=lambda x: (x.get("page_num", 0), x.get("created_at", "")))
            
            return content_list
            
        except Exception as e:
            logger.error(f"Error listing content for document {document_id}: {e}")
            return []
    
    def delete_content(self, content_hash: str) -> bool:
        """Delete content and its metadata"""
        try:
            metadata = self.get_metadata(content_hash)
            if not metadata:
                return False
            
            # Delete content file
            storage_path = Path(metadata["storage_path"])
            if storage_path.exists():
                storage_path.unlink()
            
            # Delete metadata file
            metadata_path = self.metadata_storage_path / f"{content_hash}.json"
            if metadata_path.exists():
                metadata_path.unlink()
            
            # Remove from cache
            cache_key = f"multimodal:metadata:{content_hash}"
            cache_service.delete(cache_key)
            
            logger.info(f"Deleted content: {content_hash}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting content {content_hash}: {e}")
            return False

# Global storage instance
_multimodal_storage = None

def get_multimodal_storage() -> MultimodalStorage:
    """Get the global multimodal storage instance"""
    global _multimodal_storage
    if _multimodal_storage is None:
        _multimodal_storage = MultimodalStorage()
    return _multimodal_storage
