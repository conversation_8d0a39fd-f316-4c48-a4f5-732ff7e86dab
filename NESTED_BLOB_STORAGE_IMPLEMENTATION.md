# Nested BLOB Storage Implementation

## Overview

This document describes the implementation of nested BLOB storage directory structure for multimodal content. The goal is to co-locate BLOB files (extracted images and tables) within each document's directory structure so that when `delete_file()` removes a document directory, all associated multimodal content is automatically deleted.

## Changes Made

### 1. Configuration Updates

#### `app/templates/multimodal_config_partial.html`
- **Line 661-679**: Updated BLOB storage path field
  - Changed label to "BLOB Storage Path Pattern"
  - Added placeholder showing the pattern format
  - Enhanced description to explain nested structure and placeholder usage
  - Added example and benefits explanation

#### `config/multimodal_config.py`
- **Line 118**: Changed default `blob_storage_path` from `"./data/multimodal_blobs"` to `"temp/{category}/{filename}/multimodal_blobs"`

### 2. Storage Service Refactoring

#### `app/services/multimodal_storage.py`
- **Lines 22-43**: Modified `__init__` method to support both legacy and document-specific paths
- **Lines 44-108**: Added new methods:
  - `get_document_storage_path()`: Generates document-specific storage paths using category and filename
  - `_get_storage_paths()`: Creates document-specific subdirectories (images/, tables/, metadata/)
- **Lines 118-188**: Updated `store_image()` method:
  - Added optional `category` and `filename` parameters
  - Uses document-specific paths when available
  - Falls back to legacy global storage for backward compatibility
- **Lines 190-251**: Updated `store_table()` method:
  - Added optional `category` and `filename` parameters
  - Uses document-specific paths when available
  - Falls back to legacy global storage for backward compatibility
- **Lines 305-344**: Enhanced `get_metadata()` method:
  - Added optional `category` and `filename` parameters
  - Searches both document-specific and legacy locations
- **Lines 345-359**: Added `_store_metadata_at_path()` method for storing metadata at specific paths

### 3. Directory Structure Updates

#### `scripts/setup/create_temp_dirs.py`
- **Lines 45-51**: Added multimodal BLOB storage directory definitions
- **Lines 67-72**: Added creation of multimodal directories
- **Lines 126-139**: Updated return dictionary to include new multimodal directory paths

### 4. Integration Updates

#### `app/services/multimodal_image_processor.py`
- **Lines 162-167**: Updated `store_image()` call to include category and filename parameters

#### `app/services/multimodal_table_processor.py`
- **Lines 64**: Added storage of category in instance variable for use in storage methods
- **Lines 189-195**: Updated PyMuPDF table storage to include category and filename
- **Lines 312-316**: Updated Camelot table storage to include category and filename  
- **Lines 410-414**: Updated Tabula table storage to include category and filename

## New Directory Structure

The new nested structure organizes files as follows:

```
temp/
├── {category}/
│   ├── {filename}/
│   │   ├── pdf_images/
│   │   ├── pdf_tables/
│   │   ├── pdf_text/
│   │   └── multimodal_blobs/          # NEW
│   │       ├── images/                # NEW - BLOB image files
│   │       ├── tables/                # NEW - BLOB table files
│   │       └── metadata/              # NEW - BLOB metadata files
```

## Benefits

1. **Automatic Cleanup**: When `delete_file()` removes a document directory, all associated multimodal content is automatically deleted
2. **Better Organization**: All document-related files are co-located in one directory tree
3. **Easier Backup/Migration**: Document directories are self-contained units
4. **Reduced Orphaned Files**: Eliminates the problem of orphaned BLOB files
5. **Simplified Maintenance**: No need for separate cleanup processes

## Backward Compatibility

The implementation maintains backward compatibility by:

1. **Pattern Detection**: Storage service detects if the configuration uses the new pattern (contains `{category}` and `{filename}` placeholders)
2. **Fallback Behavior**: If pattern is not detected or category/filename are not provided, falls back to legacy global storage
3. **Dual Search**: Metadata retrieval searches both document-specific and legacy locations
4. **Gradual Migration**: Existing installations can migrate gradually or continue using legacy storage

## Configuration Example

The new configuration pattern supports placeholders:

```
temp/{category}/{filename}/multimodal_blobs
```

This creates document-specific storage paths like:
```
temp/RESEARCH_PAPERS/climate_study/multimodal_blobs/
temp/TECHNICAL_DOCS/user_manual/multimodal_blobs/
```

## Testing

The implementation has been tested to verify:
- ✅ Configuration uses new nested pattern
- ✅ Directory structure creation includes multimodal directories
- ✅ Storage service uses document-specific paths
- ✅ Storage subdirectories are created automatically
- ✅ Path generation works correctly across operating systems

## Migration Notes

For existing installations:
1. Update configuration to use new pattern
2. Existing BLOB files in legacy location will continue to work
3. New documents will use nested structure
4. Optional: Create migration script to move existing files to new structure
